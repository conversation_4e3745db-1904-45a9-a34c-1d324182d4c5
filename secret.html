<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MineCraft Auction House - Exclusive Items</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: #2c2c2c;
            color: #fff;
            min-height: 100vh;
            background-image:
                radial-gradient(circle at 25% 25%, #4a4a4a 2px, transparent 2px),
                radial-gradient(circle at 75% 75%, #3a3a3a 1px, transparent 1px);
            background-size: 50px 50px, 25px 25px;
        }

        .header {
            background: #1a1a1a;
            padding: 20px 0;
            border-bottom: 3px solid #8B4513;
            text-align: center;
        }

        .header h1 {
            color: #FFD700;
            font-size: 2.5rem;
            text-shadow: 2px 2px 4px #000;
            margin-bottom: 10px;
        }

        .header p {
            color: #CCCCCC;
            font-size: 1.1rem;
        }

        .back-link {
            position: fixed;
            top: 20px;
            left: 20px;
            color: #FFD700;
            text-decoration: none;
            font-size: 1rem;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            z-index: 1000;
        }

        .back-link:hover {
            color: #ffd700;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .auction-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            margin-top: 30px;
        }

        .lot-section {
            background: #3a3a3a;
            border: 2px solid #8B4513;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.3);
        }

        .lot-title {
            color: #FFD700;
            font-size: 1.8rem;
            margin-bottom: 20px;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .lot-placeholder {
            background: #2a2a2a;
            border: 2px dashed #666;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            border-radius: 8px;
        }

        .lot-placeholder p {
            color: #888;
            font-size: 1.2rem;
            text-align: center;
        }

        .bidding-section {
            background: #3a3a3a;
            border: 2px solid #8B4513;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.3);
        }

        .timer {
            background: #1a1a1a;
            border: 2px solid #FF4444;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin-bottom: 30px;
        }

        .timer h3 {
            color: #FF4444;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .countdown {
            font-size: 2rem;
            color: #FFD700;
            font-weight: bold;
            text-shadow: 1px 1px 2px #000;
        }

        .current-bid {
            background: #1a1a1a;
            border: 2px solid #32CD32;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin-bottom: 30px;
        }

        .current-bid h3 {
            color: #32CD32;
            margin-bottom: 10px;
        }

        .bid-amount {
            font-size: 1.8rem;
            color: #FFD700;
            margin-bottom: 5px;
        }

        .bid-user {
            color: #CCCCCC;
            font-size: 1rem;
        }

        .bid-form {
            background: #2a2a2a;
            border: 2px solid #666;
            border-radius: 8px;
            padding: 25px;
        }

        .bid-form h3 {
            color: #FFD700;
            margin-bottom: 20px;
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            color: #CCCCCC;
            margin-bottom: 8px;
            font-weight: bold;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            background: #1a1a1a;
            border: 2px solid #666;
            border-radius: 5px;
            color: #fff;
            font-size: 1rem;
            font-family: 'Courier New', monospace;
        }

        .form-group input:focus {
            outline: none;
            border-color: #FFD700;
            box-shadow: 0 0 8px rgba(255, 215, 0, 0.3);
        }

        .diamond-symbol {
            color: #00FFFF;
            font-size: 1.2rem;
            margin-right: 5px;
        }

        .bid-button {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #32CD32, #228B22);
            border: none;
            border-radius: 8px;
            color: #fff;
            font-size: 1.2rem;
            font-weight: bold;
            cursor: pointer;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            font-family: 'Courier New', monospace;
        }

        .bid-button:hover {
            background: linear-gradient(45deg, #228B22, #32CD32);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(50, 205, 50, 0.4);
        }

        .bid-button:active {
            transform: translateY(0);
        }

        .auction-info {
            background: #2a2a2a;
            border: 1px solid #666;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }

        .auction-info h4 {
            color: #FFD700;
            margin-bottom: 15px;
        }

        .auction-info ul {
            list-style: none;
            color: #CCCCCC;
        }

        .auction-info li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .auction-info li:before {
            content: "⛏";
            position: absolute;
            left: 0;
            color: #8B4513;
        }

        @media (max-width: 768px) {
            .auction-grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }

            .countdown {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <a href="index.html" class="back-link">← Back to MELLONSO</a>

    <div class="header">
        <h1>🏛️ Funtimes SMP Auction House</h1>
        <p>Exclusive Items • Rare Collectibles • Limited Time Only</p>

        <!-- Emergency Reset Button (Admin Only) -->
        <div style="position: absolute; top: 20px; right: 20px;">
            <button onclick="emergencyReset()" style="
                background: linear-gradient(45deg, #ff4444, #cc0000);
                color: white;
                border: none;
                padding: 10px 15px;
                border-radius: 6px;
                font-size: 0.9rem;
                cursor: pointer;
                box-shadow: 0 4px 8px rgba(0,0,0,0.3);
                transition: transform 0.2s ease;
            " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
                🛑 EMERGENCY RESET
            </button>
        </div>
    </div>

    <div class="container">
        <div id="noAuctionsMessage" style="display: none; text-align: center; padding: 60px 20px;">
            <h2 style="color: #888; font-size: 2rem; margin-bottom: 20px;">😔 No Active Auctions</h2>
            <p style="color: #CCCCCC; font-size: 1.2rem;">Sorry there are no ongoing auctions, please check again later</p>
        </div>

        <div id="auctionContainer">
            <!-- Lots will be dynamically generated here -->
        </div>

        </div>
    </div>

    <script>
        // Load configuration from admin panel
        let auctionConfig = {
            duration: 24,
            minimumBid: 10,
            bidIncrement: 5,
            buyoutBid: null,
            numberOfLots: 1,
            lots: [
                {
                    title: "Mystery Item",
                    description: "",
                    imageData: null,
                    redeemCode: "FUNTIMES-DEFAULT-001",
                    repeatInterval: 0
                }
            ]
        };

        // Active lots tracking
        let activeLots = [];
        let lotTimers = {};
        let lastSyncTime = 0;

        // Load saved configuration
        function loadConfiguration() {
            const saved = localStorage.getItem('auctionConfig');
            if (saved) {
                auctionConfig = JSON.parse(saved);
            }

            // Check if auctions have been manually started
            const auctionStarted = localStorage.getItem('auctionStarted') === 'true';
            const auctionStartTime = localStorage.getItem('auctionStartTime');

            // Check for any lots that should be restarted
            const repeatData = JSON.parse(localStorage.getItem('repeatAuctions') || '{}');
            const now = new Date().getTime();

            console.log('Loading configuration, repeat data:', repeatData);

            // Initialize active lots from configuration
            activeLots = [];

            auctionConfig.lots.forEach((lot, index) => {
                const lotId = index + 1;
                const repeatInfo = repeatData[lotId];

                let shouldActivate = false;
                let startTime = now;

                // Check if this lot should be restarted (repeating auction)
                if (repeatInfo && repeatInfo.shouldRestart && now >= repeatInfo.restartTime) {
                    console.log(`Restarting lot ${lotId} from loadConfiguration, original end: ${new Date(repeatInfo.auctionEndTime || 0).toLocaleString()}`);
                    shouldActivate = true;
                    // Remove from repeat queue
                    delete repeatData[lotId];
                    localStorage.setItem('repeatAuctions', JSON.stringify(repeatData));
                }
                // Check if auctions have been manually started
                else if (auctionStarted && auctionStartTime) {
                    const manualStartTime = new Date(auctionStartTime).getTime();
                    // Only start if manual start was recent (within last 24 hours)
                    if (now - manualStartTime < 24 * 60 * 60 * 1000) {
                        shouldActivate = true;
                        startTime = manualStartTime;
                    } else {
                        // Clear old manual start flag
                        localStorage.removeItem('auctionStarted');
                        localStorage.removeItem('auctionStartTime');
                    }
                }

                if (shouldActivate) {
                    // Calculate end time - duration is stored in minutes
                    const durationInMs = (lot.duration || 24 * 60) * 60 * 1000; // Default 24 hours if not set

                    activeLots.push({
                        id: lotId,
                        ...lot,
                        currentBid: lot.minimumBid,
                        currentBidder: "No bids yet",
                        endTime: startTime + durationInMs,
                        isActive: true
                    });
                }
            });

            console.log('Active lots after loading:', activeLots.length);
        }

        // Generate HTML for a single lot
        function generateLotHTML(lot) {
            const buyoutSection = lot.buyoutBid ? `
                <div id="buyoutSection${lot.id}" style="margin-top: 20px;">
                    <button type="button" class="bid-button" onclick="handleBuyout(${lot.id})" style="background: linear-gradient(45deg, #FFD700, #FFA500); color: #000;">
                        💰 Buy Now for ${lot.buyoutBid} Diamonds
                    </button>
                </div>
            ` : '';

            const lotImage = lot.imageData ?
                `<img src="${lot.imageData}" alt="${lot.title}" style="max-width: 100%; max-height: 280px; border-radius: 8px;">
                 ${lot.description ? `<p style="margin-top: 15px; color: #CCCCCC; font-size: 1rem;">${lot.description}</p>` : ''}` :
                `<p>🔒 ${lot.description || 'Item details will be revealed soon...'}<br>Stay tuned for something extraordinary!</p>`;

            return `
                <div class="auction-grid" id="lot${lot.id}" style="margin-bottom: 40px;">
                    <div class="lot-section">
                        <h2 class="lot-title">📦 Lot ${lot.id} - ${lot.title}</h2>
                        <div class="lot-placeholder">
                            ${lotImage}
                        </div>

                        <div class="auction-info">
                            <h4>📋 Auction Details</h4>
                            <ul>
                                <li>Starting Bid: ${lot.minimumBid} Diamonds</li>
                                <li>Bid Increment: ${lot.bidIncrement} Diamonds minimum</li>
                                <li>Auction Type: Live Bidding</li>
                                <li>Payment: Minecraft Diamonds only</li>
                                <li>Delivery: In-game transfer</li>
                                ${lot.buyoutBid ? `<li>Buyout Price: ${lot.buyoutBid} Diamonds</li>` : ''}
                            </ul>
                        </div>
                    </div>

                    <div class="bidding-section">
                        <div class="timer">
                            <h3>⏰ Auction Ends In:</h3>
                            <div class="countdown" id="countdown${lot.id}">Loading...</div>
                        </div>

                        <div class="current-bid">
                            <h3>💎 Current Highest Bid</h3>
                            <div class="bid-amount" id="currentBid${lot.id}">${lot.currentBid} Diamonds</div>
                            <div class="bid-user" id="currentBidder${lot.id}">${lot.currentBidder}</div>
                        </div>

                        <div class="bid-form">
                            <h3>🎯 Place Your Bid</h3>
                            <form id="bidForm${lot.id}" onsubmit="handleBid(event, ${lot.id})">
                                <div class="form-group">
                                    <label for="username${lot.id}">👤 Minecraft Username:</label>
                                    <input type="text" id="username${lot.id}" name="username" placeholder="Enter your username" required>
                                </div>

                                <div class="form-group">
                                    <label for="bidAmount${lot.id}"><span class="diamond-symbol">💎</span>Bid Amount (Diamonds):</label>
                                    <input type="number" id="bidAmount${lot.id}" name="bidAmount" min="${lot.currentBid + lot.bidIncrement}" placeholder="Minimum ${lot.currentBid + lot.bidIncrement} diamonds" required>
                                </div>

                                <button type="submit" class="bid-button">🔨 Place Bid</button>
                            </form>

                            ${buyoutSection}
                        </div>
                    </div>
                </div>
            `;
        }

        // Handle bid submission
        function handleBid(event, lotId) {
            event.preventDefault();

            const lot = activeLots.find(l => l.id === lotId);
            if (!lot || !lot.isActive) return;

            const username = document.getElementById(`username${lotId}`).value.trim();
            const bidAmount = parseInt(document.getElementById(`bidAmount${lotId}`).value);

            if (!username) {
                alert('Please enter your Minecraft username!');
                return;
            }

            if (bidAmount <= lot.currentBid) {
                alert(`Your bid must be higher than the current bid of ${lot.currentBid} diamonds!`);
                return;
            }

            // Update lot bid
            lot.currentBid = bidAmount;
            lot.currentBidder = username;

            // Check if bid was placed in last minute - extend time
            const timeLeft = lot.endTime - new Date().getTime();
            if (timeLeft < 60000) { // Less than 1 minute
                lot.endTime = new Date().getTime() + 60000; // Add 1 minute
                console.log(`Bid placed in last minute, extending auction ${lotId} by 60 seconds`);
            }

            // Update display
            document.getElementById(`currentBid${lotId}`).textContent = `${lot.currentBid} Diamonds`;
            document.getElementById(`currentBidder${lotId}`).textContent = `Leading bidder: ${lot.currentBidder}`;

            // Update minimum bid for next bidder
            document.getElementById(`bidAmount${lotId}`).min = lot.currentBid + lot.bidIncrement;
            document.getElementById(`bidAmount${lotId}`).placeholder = `Minimum ${lot.currentBid + lot.bidIncrement} diamonds`;

            // Clear form
            document.getElementById(`bidForm${lotId}`).reset();

            // Sync state across tabs
            syncAuctionState();

            // Success message
            alert(`🎉 Bid placed successfully!\n\nYou are now the highest bidder with ${lot.currentBid} diamonds!\n\nGood luck, ${lot.currentBidder}!`);
        }

        // Handle buyout
        function handleBuyout(lotId) {
            const lot = activeLots.find(l => l.id === lotId);
            if (!lot || !lot.isActive) return;

            // Try to get username from the input field first
            let username = document.getElementById(`username${lotId}`)?.value?.trim();

            // If no username in field, prompt for it
            if (!username) {
                username = prompt('Enter your Minecraft username to complete the buyout:');
            }

            if (username && username.trim()) {
                if (confirm(`Confirm buyout for ${lot.buyoutBid} diamonds?\n\nUsername: ${username.trim()}`)) {
                    // Sync state before completing
                    syncAuctionState();
                    completeAuction(lotId, username.trim(), lot.buyoutBid, 'Buyout');
                }
            }
        }

        // Complete auction (win or buyout)
        function completeAuction(lotId, winner, finalBid, type) {
            const lot = activeLots.find(l => l.id === lotId);
            if (!lot) return;

            lot.isActive = false;

            // Update display
            if (type === 'Winner') {
                document.getElementById(`currentBid${lotId}`).textContent = `${finalBid} Diamonds`;
                document.getElementById(`currentBidder${lotId}`).textContent = `Winner: ${winner}!`;
                document.getElementById(`countdown${lotId}`).textContent = `WINNER: ${winner.toUpperCase()}!`;
            } else {
                document.getElementById(`currentBid${lotId}`).textContent = `${finalBid} Diamonds (${type.toUpperCase()})`;
                document.getElementById(`currentBidder${lotId}`).textContent = `Winner: ${winner}`;
                document.getElementById(`countdown${lotId}`).textContent = "AUCTION ENDED";
            }
            document.getElementById(`bidForm${lotId}`).style.display = 'none';

            const buyoutSection = document.getElementById(`buyoutSection${lotId}`);
            if (buyoutSection) buyoutSection.style.display = 'none';

            // Show redeem code
            const redeemCode = lot.redeemCode || `FUNTIMES-${Date.now()}-${lotId}`;
            alert(`🎉 Congratulations ${winner}!\n\nYou have won this auction for ${finalBid} diamonds!\n\n🎫 Your redeem code is:\n${redeemCode}\n\nUse this code on the Funtimes SMP server to claim your item!`);

            // Save to auction history
            saveAuctionHistory(lot.title, winner, finalBid, redeemCode, type);

            // Remove from repeat queue if it exists (won auctions should not repeat)
            const repeatData = JSON.parse(localStorage.getItem('repeatAuctions') || '{}');
            if (repeatData[lotId]) {
                delete repeatData[lotId];
                localStorage.setItem('repeatAuctions', JSON.stringify(repeatData));
                console.log(`Removed lot ${lotId} from repeat queue - auction was won`);
            }

            // Also remove from active lots to prevent any further processing
            const lotIndex = activeLots.findIndex(l => l.id === lotId);
            if (lotIndex >= 0) {
                activeLots.splice(lotIndex, 1);
            }

            // Sync state across tabs
            syncAuctionState();

            // Remove lot after 5 seconds
            setTimeout(() => {
                const lotElement = document.getElementById(`lot${lotId}`);
                if (lotElement) {
                    lotElement.style.transition = 'opacity 0.5s ease';
                    lotElement.style.opacity = '0';
                    setTimeout(() => {
                        lotElement.remove();
                        checkForActiveLots();
                    }, 500);
                }
            }, 5000);
        }

        // Save auction to history
        function saveAuctionHistory(lotTitle, winner, finalBid, redeemCode, type) {
            const history = JSON.parse(localStorage.getItem('auctionHistory') || '[]');
            history.unshift({
                lotTitle,
                winner,
                finalBid,
                redeemCode,
                type,
                completedAt: new Date().toISOString()
            });
            localStorage.setItem('auctionHistory', JSON.stringify(history));
        }

        // Handle repeating auction when no bids
        function handleRepeatAuction(lot) {
            lot.isActive = false;

            const countdownEl = document.getElementById(`countdown${lot.id}`);
            // Use the actual end time if available, otherwise current time
            const auctionEndTime = lot.actualEndTime || new Date().getTime();
            // repeatInterval is stored in minutes
            const nextStartTime = auctionEndTime + (lot.repeatInterval * 60 * 1000);

            console.log(`Setting up repeat for lot ${lot.id}, auction ended at: ${new Date(auctionEndTime).toLocaleString()}, restart time: ${new Date(nextStartTime).toLocaleString()}`);

            // Save repeat info with original lot configuration
            const repeatData = JSON.parse(localStorage.getItem('repeatAuctions') || '{}');
            repeatData[lot.id] = {
                shouldRestart: true,
                restartTime: nextStartTime,
                auctionEndTime: auctionEndTime, // Store when auction actually ended
                originalLot: {
                    ...lot,
                    currentBid: lot.minimumBid, // Reset to minimum bid
                    currentBidder: "No bids yet", // Reset bidder
                    isActive: false // Will be set to true when restarted
                }
            };
            localStorage.setItem('repeatAuctions', JSON.stringify(repeatData));

            console.log(`Auction ${lot.id} ended at ${new Date(auctionEndTime).toLocaleString()}, will restart at ${new Date(nextStartTime).toLocaleString()}`);

            // Sync state to other tabs
            syncAuctionState();

            // Format display time
            const displayTime = lot.repeatInterval >= 60 ?
                `${Math.floor(lot.repeatInterval / 60)}H ${lot.repeatInterval % 60}M` :
                `${lot.repeatInterval}M`;

            // Update display
            countdownEl.textContent = `RESTARTING IN ${displayTime}`;
            document.getElementById(`bidForm${lot.id}`).style.display = 'none';
            const buyoutSection = document.getElementById(`buyoutSection${lot.id}`);
            if (buyoutSection) buyoutSection.style.display = 'none';

            // Show restart message
            const lotElement = document.getElementById(`lot${lot.id}`);
            if (lotElement) {
                const restartMessage = document.createElement('div');
                restartMessage.style.cssText = `
                    background: rgba(255, 165, 0, 0.2);
                    border: 2px solid #FFA500;
                    border-radius: 8px;
                    padding: 15px;
                    margin-top: 15px;
                    text-align: center;
                    color: #FFA500;
                    font-weight: bold;
                `;
                restartMessage.innerHTML = `
                    🔄 No bids received - This auction will restart in ${displayTime}<br>
                    <small style="opacity: 0.8;">Next auction: ${new Date(nextStartTime).toLocaleString()}</small>
                `;
                lotElement.querySelector('.bidding-section').appendChild(restartMessage);
            }

            // Remove lot after showing message for 10 seconds
            setTimeout(() => {
                if (lotElement) {
                    lotElement.style.transition = 'opacity 0.5s ease';
                    lotElement.style.opacity = '0';
                    setTimeout(() => {
                        lotElement.remove();
                        checkForActiveLots();
                    }, 500);
                }
            }, 10000);
        }

        // Check if any lots are still active
        function checkForActiveLots() {
            const activeCount = activeLots.filter(lot => lot.isActive).length;
            if (activeCount === 0) {
                document.getElementById('auctionContainer').style.display = 'none';
                document.getElementById('noAuctionsMessage').style.display = 'block';
            }
        }

        // Update countdown timers for all lots
        function updateCountdowns() {
            const now = new Date().getTime();

            activeLots.forEach(lot => {
                if (!lot.isActive) return;

                const timeLeft = lot.endTime - now;
                const countdownEl = document.getElementById(`countdown${lot.id}`);

                if (timeLeft > 0) {
                    const hours = Math.floor(timeLeft / (1000 * 60 * 60));
                    const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                    const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

                    countdownEl.textContent =
                        `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                } else {
                    // Auction ended by time
                    if (lot.currentBidder !== "No bids yet") {
                        completeAuction(lot.id, lot.currentBidder, lot.currentBid, 'Winner');
                    } else {
                        // No bids - check if should repeat
                        if (lot.repeatInterval && lot.repeatInterval > 0) {
                            // Store the actual end time for accurate restart timing
                            lot.actualEndTime = new Date().getTime();
                            handleRepeatAuction(lot);
                        } else {
                            // No bids, just remove
                            countdownEl.textContent = "AUCTION ENDED - NO BIDS";
                            document.getElementById(`bidForm${lot.id}`).style.display = 'none';
                            const buyoutSection = document.getElementById(`buyoutSection${lot.id}`);
                            if (buyoutSection) buyoutSection.style.display = 'none';

                            setTimeout(() => {
                                const lotElement = document.getElementById(`lot${lot.id}`);
                                if (lotElement) {
                                    lotElement.style.transition = 'opacity 0.5s ease';
                                    lotElement.style.opacity = '0';
                                    setTimeout(() => {
                                        lotElement.remove();
                                        checkForActiveLots();
                                    }, 500);
                                }
                            }, 5000);

                            lot.isActive = false;
                        }
                    }
                }
            });
        }

        // Render all lots
        function renderLots() {
            const container = document.getElementById('auctionContainer');

            console.log('Rendering lots:', activeLots.length, 'total,', activeLots.filter(l => l.isActive).length, 'active');

            if (activeLots.length === 0) {
                container.style.display = 'none';
                document.getElementById('noAuctionsMessage').style.display = 'block';
                return;
            }

            container.innerHTML = '';
            activeLots.forEach(lot => {
                if (lot.isActive) {
                    container.innerHTML += generateLotHTML(lot);
                }
            });

            // Check if any lots are actually displayed
            if (container.innerHTML.trim() === '') {
                container.style.display = 'none';
                document.getElementById('noAuctionsMessage').style.display = 'block';
            } else {
                container.style.display = 'block';
                document.getElementById('noAuctionsMessage').style.display = 'none';
            }
        }

        // Check for auctions that should restart
        function checkForRestartingAuctions() {
            const repeatData = JSON.parse(localStorage.getItem('repeatAuctions') || '{}');
            const now = new Date().getTime();
            let hasRestarted = false;

            Object.keys(repeatData).forEach(lotId => {
                const repeatInfo = repeatData[lotId];
                if (repeatInfo.shouldRestart && now >= repeatInfo.restartTime) {
                    console.log(`Restarting auction for lot ${lotId}, original end time: ${new Date(repeatInfo.auctionEndTime || 0).toLocaleString()}`);

                    // Restart this auction
                    const originalLot = repeatInfo.originalLot;
                    const newLot = {
                        ...originalLot,
                        currentBid: originalLot.minimumBid,
                        currentBidder: "No bids yet",
                        endTime: now + ((originalLot.duration || 24 * 60) * 60 * 1000), // duration in minutes
                        isActive: true
                    };

                    // Add to active lots
                    const existingIndex = activeLots.findIndex(lot => lot.id == lotId);
                    if (existingIndex >= 0) {
                        activeLots[existingIndex] = newLot;
                    } else {
                        activeLots.push(newLot);
                    }

                    // Remove from repeat queue
                    delete repeatData[lotId];
                    hasRestarted = true;
                }
            });

            localStorage.setItem('repeatAuctions', JSON.stringify(repeatData));

            // Re-render if any auctions restarted
            if (hasRestarted) {
                renderLots();
                syncAuctionState();
            }
        }

        // Sync auction state across tabs
        function syncAuctionState() {
            const auctionState = {
                activeLots: activeLots,
                timestamp: new Date().getTime(),
                tabId: Math.random().toString(36).substr(2, 9) // Unique tab identifier
            };
            localStorage.setItem('auctionState', JSON.stringify(auctionState));
            localStorage.setItem('auctionStateUpdate', auctionState.timestamp.toString());
            lastSyncTime = auctionState.timestamp;
            console.log('Synced auction state:', activeLots.length, 'active lots');
        }

        // Load auction state from other tabs
        function loadAuctionState() {
            const saved = localStorage.getItem('auctionState');
            if (saved) {
                try {
                    const state = JSON.parse(saved);
                    if (state.timestamp > lastSyncTime) {
                        console.log('Loading auction state from other tab:', state.activeLots.length, 'lots');
                        activeLots = state.activeLots || [];
                        lastSyncTime = state.timestamp;
                        renderLots();
                        return true;
                    }
                } catch (e) {
                    console.error('Error loading auction state:', e);
                }
            }
            return false;
        }

        // Listen for storage changes (cross-tab communication)
        window.addEventListener('storage', function(e) {
            if (e.key === 'auctionStateUpdate') {
                console.log('Detected auction state change from another tab');
                loadAuctionState();
            }
        });

        // Emergency reset function (accessible from auction page)
        function emergencyReset() {
            const confirmMessage = `⚠️ EMERGENCY AUCTION RESET ⚠️

This will immediately:
• Stop all active auctions
• Clear all current bids
• Remove all auction timers
• Clear repeat queues
• Reset auction house to empty state

This action cannot be undone!

Are you absolutely sure you want to proceed?`;

            if (confirm(confirmMessage)) {
                // Clear all auction-related localStorage
                localStorage.removeItem('auctionState');
                localStorage.removeItem('auctionStateUpdate');
                localStorage.removeItem('auctionStarted');
                localStorage.removeItem('auctionStartTime');
                localStorage.removeItem('repeatAuctions');

                // Force clear auction state
                const emptyState = {
                    activeLots: [],
                    timestamp: new Date().getTime(),
                    tabId: 'emergency-reset'
                };
                localStorage.setItem('auctionState', JSON.stringify(emptyState));
                localStorage.setItem('auctionStateUpdate', emptyState.timestamp.toString());

                // Clear local state
                activeLots = [];
                lastSyncTime = emptyState.timestamp;

                // Re-render empty state
                renderLots();

                alert('✅ Emergency Reset Complete!\n\nAll auctions have been stopped and the auction house is now empty.\n\nTo start new auctions, use the admin panel.');
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Funtimes SMP Auction House - Initialized');

            // Load configuration and render lots
            loadConfiguration();

            // Check for restarting auctions
            checkForRestartingAuctions();

            // Try to load existing state first
            if (!loadAuctionState()) {
                // No existing state, render from config
                renderLots();
            }

            // Start countdown timers
            updateCountdowns();
            setInterval(updateCountdowns, 1000);

            // Check for restarting auctions every 10 seconds
            setInterval(checkForRestartingAuctions, 10000);

            // Force initial sync
            syncAuctionState();

            // Sync state across tabs every 1 second for better responsiveness
            setInterval(() => {
                loadAuctionState();
            }, 1000);

            // Also sync our state every 5 seconds
            setInterval(() => {
                syncAuctionState();
            }, 5000);

            // Add some visual effects
            document.body.style.opacity = '0';
            setTimeout(() => {
                document.body.style.transition = 'opacity 0.5s ease';
                document.body.style.opacity = '1';
            }, 100);
        });

        // Add keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                window.location.href = 'index.html';
            }
        });
    </script>
</body>
</html>
