<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MineCraft Auction House - Exclusive Items</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Courier New', monospace;
            background: #2c2c2c;
            color: #fff;
            min-height: 100vh;
            background-image:
                radial-gradient(circle at 25% 25%, #4a4a4a 2px, transparent 2px),
                radial-gradient(circle at 75% 75%, #3a3a3a 1px, transparent 1px);
            background-size: 50px 50px, 25px 25px;
        }

        .header {
            background: #1a1a1a;
            padding: 20px 0;
            border-bottom: 3px solid #8B4513;
            text-align: center;
        }

        .header h1 {
            color: #FFD700;
            font-size: 2.5rem;
            text-shadow: 2px 2px 4px #000;
            margin-bottom: 10px;
        }

        .header p {
            color: #CCCCCC;
            font-size: 1.1rem;
        }

        .back-link {
            position: fixed;
            top: 20px;
            left: 20px;
            color: #FFD700;
            text-decoration: none;
            font-size: 1rem;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            z-index: 1000;
        }

        .back-link:hover {
            color: #ffd700;
            text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .auction-grid {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            margin-top: 30px;
        }

        .lot-section {
            background: #3a3a3a;
            border: 2px solid #8B4513;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.3);
        }

        .lot-title {
            color: #FFD700;
            font-size: 1.8rem;
            margin-bottom: 20px;
            text-align: center;
            text-transform: uppercase;
            letter-spacing: 2px;
        }

        .lot-placeholder {
            background: #2a2a2a;
            border: 2px dashed #666;
            height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            border-radius: 8px;
        }

        .lot-placeholder p {
            color: #888;
            font-size: 1.2rem;
            text-align: center;
        }

        .bidding-section {
            background: #3a3a3a;
            border: 2px solid #8B4513;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.3);
        }

        .timer {
            background: #1a1a1a;
            border: 2px solid #FF4444;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin-bottom: 30px;
        }

        .timer h3 {
            color: #FF4444;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }

        .countdown {
            font-size: 2rem;
            color: #FFD700;
            font-weight: bold;
            text-shadow: 1px 1px 2px #000;
        }

        .current-bid {
            background: #1a1a1a;
            border: 2px solid #32CD32;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            margin-bottom: 30px;
        }

        .current-bid h3 {
            color: #32CD32;
            margin-bottom: 10px;
        }

        .bid-amount {
            font-size: 1.8rem;
            color: #FFD700;
            margin-bottom: 5px;
        }

        .bid-user {
            color: #CCCCCC;
            font-size: 1rem;
        }

        .bid-form {
            background: #2a2a2a;
            border: 2px solid #666;
            border-radius: 8px;
            padding: 25px;
        }

        .bid-form h3 {
            color: #FFD700;
            margin-bottom: 20px;
            text-align: center;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            color: #CCCCCC;
            margin-bottom: 8px;
            font-weight: bold;
        }

        .form-group input {
            width: 100%;
            padding: 12px;
            background: #1a1a1a;
            border: 2px solid #666;
            border-radius: 5px;
            color: #fff;
            font-size: 1rem;
            font-family: 'Courier New', monospace;
        }

        .form-group input:focus {
            outline: none;
            border-color: #FFD700;
            box-shadow: 0 0 8px rgba(255, 215, 0, 0.3);
        }

        .diamond-symbol {
            color: #00FFFF;
            font-size: 1.2rem;
            margin-right: 5px;
        }

        .bid-button {
            width: 100%;
            padding: 15px;
            background: linear-gradient(45deg, #32CD32, #228B22);
            border: none;
            border-radius: 8px;
            color: #fff;
            font-size: 1.2rem;
            font-weight: bold;
            cursor: pointer;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            font-family: 'Courier New', monospace;
        }

        .bid-button:hover {
            background: linear-gradient(45deg, #228B22, #32CD32);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(50, 205, 50, 0.4);
        }

        .bid-button:active {
            transform: translateY(0);
        }

        .auction-info {
            background: #2a2a2a;
            border: 1px solid #666;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }

        .auction-info h4 {
            color: #FFD700;
            margin-bottom: 15px;
        }

        .auction-info ul {
            list-style: none;
            color: #CCCCCC;
        }

        .auction-info li {
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }

        .auction-info li:before {
            content: "⛏";
            position: absolute;
            left: 0;
            color: #8B4513;
        }

        @media (max-width: 768px) {
            .auction-grid {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2rem;
            }

            .countdown {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <a href="index.html" class="back-link">← Back to MELLONSO</a>

    <div class="header">
        <h1>🏛️ MineCraft Auction House</h1>
        <p>Exclusive Items • Rare Collectibles • Limited Time Only</p>
    </div>

    <div class="container">
        <div class="auction-grid">
            <div class="lot-section">
                <h2 class="lot-title" id="lotTitle">📦 Lot 1 - Mystery Item</h2>
                <div class="lot-placeholder" id="lotDisplay">
                    <p>🔒 Item details will be revealed soon...<br>Stay tuned for something extraordinary!</p>
                </div>

                <div class="auction-info">
                    <h4>📋 Auction Details</h4>
                    <ul id="auctionDetails">
                        <li>Starting Bid: <span id="startingBidDisplay">10</span> Diamonds</li>
                        <li>Bid Increment: 5 Diamonds minimum</li>
                        <li>Auction Type: Live Bidding</li>
                        <li>Payment: Minecraft Diamonds only</li>
                        <li>Delivery: In-game transfer</li>
                        <li id="buyoutInfo" style="display: none;">Buyout Price: <span id="buyoutDisplay"></span> Diamonds</li>
                    </ul>
                </div>
            </div>

            <div class="bidding-section">
                <div class="timer">
                    <h3>⏰ Auction Ends In:</h3>
                    <div class="countdown" id="countdown">Loading...</div>
                </div>

                <div class="current-bid">
                    <h3>💎 Current Highest Bid</h3>
                    <div class="bid-amount" id="currentBid">10 Diamonds</div>
                    <div class="bid-user" id="currentBidder">No bids yet</div>
                </div>

                <div class="bid-form">
                    <h3>🎯 Place Your Bid</h3>
                    <form id="bidForm">
                        <div class="form-group">
                            <label for="username">👤 Minecraft Username:</label>
                            <input type="text" id="username" name="username" placeholder="Enter your username" required>
                        </div>

                        <div class="form-group">
                            <label for="bidAmount"><span class="diamond-symbol">💎</span>Bid Amount (Diamonds):</label>
                            <input type="number" id="bidAmount" name="bidAmount" min="15" placeholder="Minimum 15 diamonds" required>
                        </div>

                        <button type="submit" class="bid-button">🔨 Place Bid</button>
                    </form>

                    <div id="buyoutSection" style="display: none; margin-top: 20px;">
                        <button type="button" class="bid-button" id="buyoutButton" style="background: linear-gradient(45deg, #FFD700, #FFA500); color: #000;">
                            💰 Buy Now for <span id="buyoutAmount"></span> Diamonds
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Load configuration from admin panel
        let auctionConfig = {
            duration: 24,
            minimumBid: 10,
            buyoutBid: null,
            numberOfLots: 1,
            lots: [
                {
                    title: "Mystery Item",
                    description: "",
                    imageData: null
                }
            ]
        };

        // Load saved configuration
        function loadConfiguration() {
            const saved = localStorage.getItem('auctionConfig');
            if (saved) {
                auctionConfig = JSON.parse(saved);
            }
        }

        // Initialize auction settings
        loadConfiguration();

        // Auction end time (based on configured duration)
        const auctionEndTime = new Date().getTime() + (auctionConfig.duration * 60 * 60 * 1000);

        // Current bid tracking
        let currentBidAmount = auctionConfig.minimumBid;
        let currentBidderName = "No bids yet";

        // Update countdown timer
        function updateCountdown() {
            const now = new Date().getTime();
            const timeLeft = auctionEndTime - now;

            if (timeLeft > 0) {
                const hours = Math.floor(timeLeft / (1000 * 60 * 60));
                const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

                document.getElementById('countdown').textContent =
                    `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            } else {
                document.getElementById('countdown').textContent = "AUCTION ENDED";
                document.getElementById('bidForm').style.display = 'none';
            }
        }

        // Handle bid submission
        document.getElementById('bidForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value.trim();
            const bidAmount = parseInt(document.getElementById('bidAmount').value);

            if (!username) {
                alert('Please enter your Minecraft username!');
                return;
            }

            if (bidAmount <= currentBidAmount) {
                alert(`Your bid must be higher than the current bid of ${currentBidAmount} diamonds!`);
                return;
            }

            // Update current bid
            currentBidAmount = bidAmount;
            currentBidderName = username;

            // Update display
            document.getElementById('currentBid').textContent = `${currentBidAmount} Diamonds`;
            document.getElementById('currentBidder').textContent = `Leading bidder: ${currentBidderName}`;

            // Update minimum bid for next bidder
            document.getElementById('bidAmount').min = currentBidAmount + 5;
            document.getElementById('bidAmount').placeholder = `Minimum ${currentBidAmount + 5} diamonds`;

            // Clear form
            document.getElementById('bidForm').reset();

            // Success message
            alert(`🎉 Bid placed successfully!\n\nYou are now the highest bidder with ${currentBidAmount} diamonds!\n\nGood luck, ${currentBidderName}!`);
        });

        // Handle buyout button
        document.getElementById('buyoutButton').addEventListener('click', function() {
            const username = prompt('Enter your Minecraft username to complete the buyout:');
            if (username && username.trim()) {
                if (confirm(`Confirm buyout for ${auctionConfig.buyoutBid} diamonds?\n\nUsername: ${username.trim()}`)) {
                    // End auction with buyout
                    currentBidAmount = auctionConfig.buyoutBid;
                    currentBidderName = username.trim();

                    document.getElementById('currentBid').textContent = `${currentBidAmount} Diamonds (BUYOUT)`;
                    document.getElementById('currentBidder').textContent = `Winner: ${currentBidderName}`;
                    document.getElementById('countdown').textContent = "AUCTION ENDED - BUYOUT";
                    document.getElementById('bidForm').style.display = 'none';
                    document.getElementById('buyoutSection').style.display = 'none';

                    alert(`🎉 Congratulations ${currentBidderName}!\n\nYou have successfully purchased this item for ${currentBidAmount} diamonds!\n\nThe auction has ended.`);
                }
            }
        });

        // Start countdown timer
        updateCountdown();
        setInterval(updateCountdown, 1000);

        // Update page display with configuration
        function updatePageDisplay() {
            // Update lot title
            document.getElementById('lotTitle').textContent = `📦 Lot 1 - ${auctionConfig.lots[0].title}`;

            // Update lot display
            const lotDisplay = document.getElementById('lotDisplay');
            if (auctionConfig.lots[0].imageData) {
                lotDisplay.innerHTML = `<img src="${auctionConfig.lots[0].imageData}" alt="${auctionConfig.lots[0].title}" style="max-width: 100%; max-height: 280px; border-radius: 8px;">`;
                if (auctionConfig.lots[0].description) {
                    lotDisplay.innerHTML += `<p style="margin-top: 15px; color: #CCCCCC; font-size: 1rem;">${auctionConfig.lots[0].description}</p>`;
                }
            } else {
                lotDisplay.innerHTML = `<p>🔒 ${auctionConfig.lots[0].description || 'Item details will be revealed soon...'}<br>Stay tuned for something extraordinary!</p>`;
            }

            // Update starting bid display
            document.getElementById('startingBidDisplay').textContent = auctionConfig.minimumBid;

            // Update current bid display
            document.getElementById('currentBid').textContent = `${currentBidAmount} Diamonds`;

            // Update minimum bid input
            document.getElementById('bidAmount').min = currentBidAmount + 5;
            document.getElementById('bidAmount').placeholder = `Minimum ${currentBidAmount + 5} diamonds`;

            // Show/hide buyout info
            if (auctionConfig.buyoutBid) {
                document.getElementById('buyoutInfo').style.display = 'list-item';
                document.getElementById('buyoutDisplay').textContent = auctionConfig.buyoutBid;

                // Show buyout button
                document.getElementById('buyoutSection').style.display = 'block';
                document.getElementById('buyoutAmount').textContent = auctionConfig.buyoutBid;
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('MineCraft Auction House - Initialized');

            // Update display with configuration
            updatePageDisplay();

            // Add some visual effects
            document.body.style.opacity = '0';
            setTimeout(() => {
                document.body.style.transition = 'opacity 0.5s ease';
                document.body.style.opacity = '1';
            }, 100);
        });

        // Add keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                window.location.href = 'index.html';
            }
        });
    </script>
</body>
</html>
