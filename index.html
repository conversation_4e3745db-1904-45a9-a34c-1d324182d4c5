<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MELLONSO</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Helvetica Neue', Arial, sans-serif;
            background: #0a0a0a;
            color: #ffffff;
            overflow-x: hidden;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Navigation */
        nav {
            position: fixed;
            top: 0;
            width: 100%;
            background: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(20px);
            z-index: 1000;
            padding: 20px 0;
            transition: all 0.3s ease;
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .logo {
            font-size: 2rem;
            font-weight: 300;
            letter-spacing: 8px;
            color: #d4af37;
            text-decoration: none;
        }

        .nav-links {
            display: flex;
            list-style: none;
            gap: 40px;
        }

        .nav-links a {
            color: #ffffff;
            text-decoration: none;
            font-weight: 300;
            letter-spacing: 1px;
            transition: color 0.3s ease;
            text-transform: uppercase;
            font-size: 0.9rem;
        }

        .nav-links a:hover {
            color: #d4af37;
        }

        /* Hero Section */
        .hero {
            height: 100vh;
            background: linear-gradient(135deg, rgba(0,0,0,0.7), rgba(0,0,0,0.3)),
                        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1200 800"><defs><linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23111111;stop-opacity:1" /><stop offset="100%" style="stop-color:%23000000;stop-opacity:1" /></linearGradient></defs><rect width="1200" height="800" fill="url(%23grad1)"/></svg>');
            background-size: cover;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            position: relative;
        }

        .hero-content h1 {
            font-size: 4.5rem;
            font-weight: 100;
            letter-spacing: 6px;
            margin-bottom: 20px;
            text-transform: uppercase;
            background: linear-gradient(45deg, #d4af37, #ffd700, #d4af37);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: shimmer 3s infinite;
        }

        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        .hero-content p {
            font-size: 1.2rem;
            margin-bottom: 40px;
            font-weight: 300;
            opacity: 0.9;
            letter-spacing: 2px;
        }

        .cta-button {
            background: linear-gradient(45deg, #d4af37, #ffd700);
            color: #000;
            padding: 18px 40px;
            border: none;
            font-size: 1rem;
            font-weight: 600;
            letter-spacing: 2px;
            text-transform: uppercase;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            position: relative;
            overflow: hidden;
        }

        .cta-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(212, 175, 55, 0.4);
        }

        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .cta-button:hover::before {
            left: 100%;
        }

        /* Features Section */
        .features {
            padding: 120px 0;
            background: #0f0f0f;
        }

        .section-title {
            text-align: center;
            font-size: 3rem;
            font-weight: 100;
            letter-spacing: 4px;
            margin-bottom: 80px;
            color: #d4af37;
            text-transform: uppercase;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 60px;
            margin-top: 80px;
        }

        .feature-card {
            background: linear-gradient(135deg, rgba(26, 26, 26, 0.9), rgba(17, 17, 17, 0.9));
            padding: 60px 40px;
            border: 1px solid #333;
            transition: all 0.4s ease;
            position: relative;
            overflow: hidden;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }

        /* Specific backgrounds for each feature card */
        .feature-card:nth-child(1) {
            /* Precision Engineering - Underground melon farm screenshot */
            background-image:
                linear-gradient(135deg, rgba(26, 26, 26, 0.6), rgba(17, 17, 17, 0.6)),
                url('melon-farm.png');
            background-size: 100%, cover;
            background-position: center, center;
            background-repeat: no-repeat, no-repeat;
        }

        .feature-card:nth-child(2) {
            /* Beautify Simple - Melon block on gold surface screenshot */
            background-image:
                linear-gradient(135deg, rgba(26, 26, 26, 0.6), rgba(17, 17, 17, 0.6)),
                url('melon-block.png');
            background-size: 100%, cover;
            background-position: center, center;
            background-repeat: no-repeat, no-repeat;
        }

        .feature-card:nth-child(3) {
            /* Unmatched Efficiency - Chest inventory screenshot */
            background-image:
                linear-gradient(135deg, rgba(26, 26, 26, 0.6), rgba(17, 17, 17, 0.6)),
                url('chest-inventory.png');
            background-size: 100%, cover;
            background-position: center, center;
            background-repeat: no-repeat, no-repeat;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #d4af37, #ffd700, #d4af37);
            transform: scaleX(0);
            transition: transform 0.4s ease;
        }

        .feature-card:hover::before {
            transform: scaleX(1);
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(212, 175, 55, 0.1);
            border-color: #d4af37;
        }

        .feature-card h3 {
            font-size: 1.5rem;
            font-weight: 300;
            margin-bottom: 20px;
            color: #d4af37;
            letter-spacing: 2px;
            text-transform: uppercase;
        }

        .feature-card p {
            font-size: 1rem;
            line-height: 1.8;
            opacity: 0.9;
            font-weight: 300;
        }

        /* Stats Section */
        .stats {
            padding: 100px 0;
            background: #000;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            text-align: center;
        }

        .stat-item {
            padding: 40px 20px;
            background: linear-gradient(135deg, #1a1a1a, #0a0a0a);
            border: 1px solid #333;
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            border-color: #d4af37;
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 100;
            color: #d4af37;
            display: block;
            margin-bottom: 10px;
            letter-spacing: 2px;
        }

        .stat-label {
            font-size: 1rem;
            opacity: 0.8;
            text-transform: uppercase;
            letter-spacing: 1px;
            font-weight: 300;
        }

        /* Interactive Section */
        .interactive {
            padding: 120px 0;
            background: #0f0f0f;
            text-align: center;
        }

        .luxury-counter {
            background: linear-gradient(135deg, #1a1a1a, #111111);
            padding: 80px 60px;
            border: 1px solid #333;
            max-width: 600px;
            margin: 0 auto;
            position: relative;
            overflow: hidden;
        }

        .luxury-counter::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(212, 175, 55, 0.1), transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .luxury-counter:hover::before {
            opacity: 1;
        }

        .counter-display {
            font-size: 4rem;
            font-weight: 100;
            color: #d4af37;
            margin: 40px 0;
            letter-spacing: 4px;
        }

        .counter-controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 40px;
        }

        .counter-btn {
            background: transparent;
            border: 2px solid #d4af37;
            color: #d4af37;
            padding: 15px 30px;
            font-size: 1.1rem;
            font-weight: 300;
            letter-spacing: 1px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            min-width: 80px;
        }

        .counter-btn:hover {
            background: #d4af37;
            color: #000;
            transform: translateY(-2px);
        }

        /* Footer */
        footer {
            background: #000;
            padding: 60px 0;
            text-align: center;
            border-top: 1px solid #333;
        }

        .footer-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 40px;
            margin-bottom: 40px;
        }

        .footer-section h4 {
            color: #d4af37;
            font-size: 1.2rem;
            font-weight: 300;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .footer-section p {
            opacity: 0.7;
            font-weight: 300;
            line-height: 1.6;
        }

        .copyright {
            border-top: 1px solid #333;
            padding-top: 40px;
            opacity: 0.5;
            font-weight: 300;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .nav-links {
                display: none;
            }

            .hero-content h1 {
                font-size: 2.5rem;
                letter-spacing: 3px;
            }

            .hero-content p {
                font-size: 1rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }

            .counter-controls {
                flex-direction: column;
                align-items: center;
            }

            .counter-btn {
                width: 200px;
            }
        }

        /* Floating Particles */
        .particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            width: 16px;
            height: 16px;
            background:
                /* Simple Minecraft Melon Slice - using box-shadow for pixel art */
                #FF6B47;
            border: 2px solid #2D5016;
            opacity: 1;
            animation: float 6s infinite ease-in-out, sparkle 2s infinite ease-in-out;
            image-rendering: pixelated;
            box-shadow:
                /* Creating pixel art effect with box-shadows */
                inset 2px 2px 0 #FFD700,
                inset -2px -2px 0 #C0392B,
                inset 4px 0 0 #FFA500,
                0 0 8px rgba(255, 215, 0, 0.6);
        }

        @keyframes sparkle {
            0%, 100% {
                filter: brightness(1) saturate(1);
                box-shadow:
                    inset 2px 2px 0 #FFD700,
                    inset -2px -2px 0 #C0392B,
                    inset 4px 0 0 #FFA500,
                    0 0 8px rgba(255, 215, 0, 0.6);
            }
            50% {
                filter: brightness(1.4) saturate(1.3);
                box-shadow:
                    inset 2px 2px 0 #FFD700,
                    inset -2px -2px 0 #C0392B,
                    inset 4px 0 0 #FFA500,
                    0 0 16px rgba(255, 215, 0, 0.9);
            }
        }

        .particle:nth-child(1) {
            left: 10%;
            animation-delay: 0s;
            animation-duration: 8s;
        }

        .particle:nth-child(2) {
            left: 30%;
            animation-delay: 2s;
            animation-duration: 6s;
        }

        .particle:nth-child(3) {
            left: 50%;
            animation-delay: 4s;
            animation-duration: 10s;
        }

        .particle:nth-child(4) {
            left: 70%;
            animation-delay: 1s;
            animation-duration: 7s;
        }

        .particle:nth-child(5) {
            left: 85%;
            animation-delay: 3s;
            animation-duration: 9s;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) rotate(0deg);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            50% {
                transform: translateY(50vh) rotate(180deg);
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-20vh) rotate(360deg);
                opacity: 0;
            }
        }

        /* Feature Icons */
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 30px;
            display: block;
            text-align: center;
            filter: grayscale(100%);
            transition: all 0.3s ease;
        }

        .feature-card:hover .feature-icon {
            filter: grayscale(0%);
            transform: scale(1.1);
        }

        /* Gallery Section */
        .gallery {
            padding: 120px 0;
            background: #000;
        }

        .gallery-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 40px;
            margin-top: 80px;
        }

        .gallery-item {
            position: relative;
            height: 300px;
            overflow: hidden;
            border: 1px solid #333;
            transition: all 0.4s ease;
        }

        .gallery-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(212, 175, 55, 0.2);
            border-color: #d4af37;
        }

        .gallery-image {
            width: 100%;
            height: 100%;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.4s ease;
        }

        .gallery-overlay {
            text-align: center;
            z-index: 2;
            transition: all 0.3s ease;
        }

        .gallery-overlay h4 {
            font-size: 1.8rem;
            font-weight: 300;
            color: #d4af37;
            margin-bottom: 10px;
            letter-spacing: 2px;
            text-transform: uppercase;
        }

        .gallery-overlay p {
            font-size: 1rem;
            opacity: 0.8;
            letter-spacing: 1px;
        }

        .gallery-item:hover .gallery-image {
            transform: scale(1.05);
        }

        .gallery-item:hover .gallery-overlay {
            transform: translateY(-10px);
        }

        /* Enhanced Mobile Responsiveness */
        @media (max-width: 768px) {
            .gallery-grid {
                grid-template-columns: 1fr;
            }

            .gallery-item {
                height: 250px;
            }

            .particles {
                display: none;
            }
        }

        /* Scroll animations */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.8s ease;
        }

        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Enhanced Navigation */
        .nav-container::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 1px;
            background: linear-gradient(90deg, transparent, #d4af37, transparent);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        nav:hover .nav-container::before {
            opacity: 1;
        }

        /* Mobile Menu */
        .mobile-menu-toggle {
            display: none;
            flex-direction: column;
            cursor: pointer;
            gap: 4px;
        }

        .mobile-menu-toggle span {
            width: 25px;
            height: 2px;
            background: #d4af37;
            transition: all 0.3s ease;
        }

        @media (max-width: 768px) {
            .mobile-menu-toggle {
                display: flex;
            }

            .nav-links {
                position: fixed;
                top: 80px;
                right: -100%;
                width: 100%;
                height: calc(100vh - 80px);
                background: rgba(0, 0, 0, 0.95);
                flex-direction: column;
                justify-content: flex-start;
                align-items: center;
                padding-top: 50px;
                transition: right 0.3s ease;
            }

            .nav-links.active {
                right: 0;
            }

            .nav-links li {
                margin: 20px 0;
            }
        }
    </style>
</head>
<body>
    <nav>
        <div class="nav-container">
            <a href="#" class="logo">MELLONSO</a>
            <ul class="nav-links" id="navLinks">
                <li><a href="#home">Home</a></li>
                <li><a href="#collections">Collections</a></li>
            </ul>
            <div class="mobile-menu-toggle" onclick="toggleMobileMenu()">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <section class="hero" id="home">
        <div class="hero-content">
            <h1>Uncompromising Excellence</h1>
            <p>Elegance in every block</p>
            <button class="cta-button" onclick="exploreCollection()">Explore Collection</button>
        </div>
        <!-- Floating particles animation -->
        <div class="particles">
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
            <div class="particle"></div>
        </div>
    </section>

    <section class="features" id="collections">
        <div class="container">
            <h2 class="section-title fade-in">Masterpieces</h2>
            <div class="features-grid">
                <div class="feature-card fade-in">
                    <div class="feature-icon">⚡</div>
                    <h3>Precision Engineering</h3>
                    <p>Every component crafted with meticulous attention to detail, representing the pinnacle of automotive engineering and design excellence.</p>
                </div>
                <div class="feature-card fade-in">
                    <div class="feature-icon">✨</div>
                    <h3>Beautify Simple</h3>
                    <p>Transforming complexity into elegant simplicity, where every element serves a purpose and beauty emerges from refined minimalism.</p>
                </div>
                <div class="feature-card fade-in">
                    <div class="feature-icon">🏆</div>
                    <h3>Unmatched Efficiency</h3>
                    <p>Optimized performance through intelligent design, delivering maximum impact with minimal effort while maintaining the highest standards of excellence.</p>
                </div>
            </div>
        </div>
    </section>

    <section class="stats" id="heritage">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-item fade-in">
                    <span class="stat-number" id="heritage-years">0</span>
                    <span class="stat-label">Years of Heritage</span>
                </div>
                <div class="stat-item fade-in">
                    <span class="stat-number" id="master-craftsmen">1</span>
                    <span class="stat-label">Master Craftsmen</span>
                </div>
                <div class="stat-item fade-in">
                    <span class="stat-number" id="exclusive-pieces">3</span>
                    <span class="stat-label">Exclusive Pieces</span>
                </div>
                <div class="stat-item fade-in">
                    <span class="stat-number" id="global-presence">1</span>
                    <span class="stat-label">Global Locations</span>
                </div>
            </div>
        </div>
    </section>

    <section class="interactive">
        <div class="container">
            <h2 class="section-title fade-in">Experience Excellence</h2>
            <div class="luxury-counter fade-in">
                <h3 style="color: #d4af37; margin-bottom: 20px; font-weight: 300; letter-spacing: 2px; text-transform: uppercase;">Satisfied Customers</h3>
                <div class="counter-display" id="counter">0</div>
            </div>

            <!-- Disclaimer -->
            <div class="disclaimer fade-in" style="margin-top: 60px; text-align: center;">
                <p style="color: #888; font-size: 0.9rem; font-style: italic;">This is a joke website and is not meant to be taken seriously.</p>
            </div>

            <!-- Search Bar -->
            <div class="search-container fade-in" style="margin-top: 40px; text-align: center;">
                <input type="text" id="searchInput" placeholder="Search..." style="
                    background: rgba(26, 26, 26, 0.8);
                    border: 2px solid #333;
                    color: #fff;
                    padding: 15px 20px;
                    font-size: 1rem;
                    width: 300px;
                    max-width: 90%;
                    text-align: center;
                    letter-spacing: 1px;
                    transition: all 0.3s ease;
                " onfocus="this.style.borderColor='#d4af37'" onblur="this.style.borderColor='#333'">
            </div>
        </div>
    </section>



    <footer id="contact">
        <div class="container">
            <div class="copyright">
                <p>MELLONSO - Premium Minecraft Solutions</p>
            </div>
        </div>
    </footer>

    <script>
        let counter = 0;
        let timeActive = 0;
        const colors = ['#d4af37', '#ffd700', '#daa520', '#b8860b', '#cd853f'];
        let colorIndex = 0;

        function exploreCollection() {
            const messages = [
                "✨ Welcome to the world of uncompromising luxury",
                "🏆 Experience the pinnacle of craftsmanship",
                "💎 Discover your perfect masterpiece",
                "⚡ Excellence awaits your discerning taste",
                "🌟 Where innovation meets timeless elegance"
            ];

            // Create a custom modal instead of alert
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.9);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;

            const content = document.createElement('div');
            content.style.cssText = `
                background: linear-gradient(135deg, #1a1a1a, #111111);
                padding: 60px 40px;
                border: 2px solid #d4af37;
                text-align: center;
                max-width: 500px;
                color: white;
                font-family: 'Helvetica Neue', Arial, sans-serif;
                transform: scale(0.8);
                transition: transform 0.3s ease;
            `;

            const message = messages[Math.floor(Math.random() * messages.length)];
            content.innerHTML = `
                <h3 style="color: #d4af37; margin-bottom: 20px; font-weight: 300; letter-spacing: 2px; text-transform: uppercase;">MELLONSO</h3>
                <p style="font-size: 1.2rem; margin-bottom: 30px; line-height: 1.6;">${message}</p>
                <button onclick="this.parentElement.parentElement.remove()" style="
                    background: linear-gradient(45deg, #d4af37, #ffd700);
                    color: #000;
                    border: none;
                    padding: 15px 30px;
                    font-weight: 600;
                    letter-spacing: 1px;
                    text-transform: uppercase;
                    cursor: pointer;
                    transition: all 0.3s ease;
                ">Continue</button>
            `;

            modal.appendChild(content);
            document.body.appendChild(modal);

            setTimeout(() => {
                modal.style.opacity = '1';
                content.style.transform = 'scale(1)';
            }, 10);
        }

        // Search functionality
        function handleSearch() {
            const searchInput = document.getElementById('searchInput');
            const searchValue = searchInput.value.toLowerCase();

            if (searchValue === 'supersecreatpassword') {
                window.location.href = 'secret.html';
            } else if (searchValue === 'supersecretpassword1') {
                window.location.href = 'admin.html';
            }
        }

        // Add search event listeners
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput) {
                searchInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        handleSearch();
                    }
                });

                searchInput.addEventListener('input', function() {
                    // Check on every input change
                    handleSearch();
                });
            }
        });

        // Mobile menu toggle
        function toggleMobileMenu() {
            const navLinks = document.getElementById('navLinks');
            navLinks.classList.toggle('active');
        }

        // Animated counter for stats
        function animateStats() {
            const stats = [
                { id: 'heritage-years', target: 0, suffix: '' },
                { id: 'master-craftsmen', target: 1, suffix: '' },
                { id: 'exclusive-pieces', target: 3, suffix: '' },
                { id: 'global-presence', target: 1, suffix: '' }
            ];

            stats.forEach(stat => {
                const element = document.getElementById(stat.id);
                if (element && !element.dataset.animated) {
                    element.dataset.animated = 'true';
                    let current = 0;
                    const increment = stat.target / 50;
                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= stat.target) {
                            current = stat.target;
                            clearInterval(timer);
                        }
                        element.textContent = Math.floor(current).toLocaleString() + stat.suffix;
                    }, 50);
                }
            });
        }

        // Enhanced scroll animations
        function checkScroll() {
            const elements = document.querySelectorAll('.fade-in');
            elements.forEach(element => {
                const elementTop = element.getBoundingClientRect().top;
                const elementVisible = 150;

                if (elementTop < window.innerHeight - elementVisible) {
                    element.classList.add('visible');

                    // Trigger stats animation when stats section is visible
                    if (element.closest('.stats')) {
                        animateStats();
                    }
                }
            });
        }

        // Parallax effect for hero section
        function handleParallax() {
            const scrolled = window.pageYOffset;
            const hero = document.querySelector('.hero');
            if (hero) {
                hero.style.transform = `translateY(${scrolled * 0.5}px)`;
            }
        }

        // Enhanced navigation background on scroll
        function handleNavScroll() {
            const nav = document.querySelector('nav');
            if (window.scrollY > 50) {
                nav.style.background = 'rgba(0, 0, 0, 0.95)';
                nav.style.padding = '15px 0';
            } else {
                nav.style.background = 'rgba(0, 0, 0, 0.9)';
                nav.style.padding = '20px 0';
            }
        }

        // Event listeners
        window.addEventListener('scroll', () => {
            checkScroll();
            handleParallax();
            handleNavScroll();
        });

        window.addEventListener('load', checkScroll);

        // Smooth scrolling for navigation
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    // Close mobile menu if open
                    document.getElementById('navLinks').classList.remove('active');

                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            const navLinks = document.getElementById('navLinks');
            const mobileToggle = document.querySelector('.mobile-menu-toggle');

            if (!navLinks.contains(e.target) && !mobileToggle.contains(e.target)) {
                navLinks.classList.remove('active');
            }
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            console.log('MELLONSO - Luxury Experience Initialized');
            checkScroll();

            // Add loading animation
            document.body.style.opacity = '0';
            setTimeout(() => {
                document.body.style.transition = 'opacity 0.5s ease';
                document.body.style.opacity = '1';
            }, 100);
        });

        // Add keyboard navigation
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                document.getElementById('navLinks').classList.remove('active');
                // Close any modals
                const modals = document.querySelectorAll('[style*="position: fixed"]');
                modals.forEach(modal => modal.remove());
            }
        });
    </script>
</body>
</html>
